import CountriesGlobe from './CountriesGlobe';
import RecentSearchesCard from './RecentSearchesCard';
import {Image } from '@heroui/react';
const RecentSearches = () => {
  return (
    <div className="my-5">
      <div>
        <p className="text-4xl font-semibold">Your recent searches</p>
        <div>
          <div className='flex flex-row items-center gap-4 my-4'>
            <Image
              alt='https://heroui.com/images/hero-card.jpeg'
              className="min-w-full object-cover shadow-none"
              radius="sm"
              src='https://heroui.com/images/hero-card.jpeg'
              width={100}
              height={100}
            />

            <div className="">
              <p className="text-base font-bold text-start text-[#25233A]">
                Istanbul, Turkey
              </p>
              <p className="text-[#868383] text-sm mt-1 leading-6 items-end">
               325 places
              </p>
            </div>
          </div>
        </div>
      </div>
      <CountriesGlobe />
      <div className="mt-4">
        <RecentSearchesCard />
      </div>
    </div>
  );
};

export default RecentSearches;
