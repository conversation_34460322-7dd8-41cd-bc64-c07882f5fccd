'use client';

import { useState, useEffect } from 'react';

import LandingPage from '@/screen/landing';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);
  const [isLogin, setIsLogin] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      const loginEmail = localStorage.getItem('userEmailOrPhone');
      const loginPassword = localStorage.getItem('userPassword');
      setIsLogin(
        loginEmail === '<EMAIL>' &&
          loginPassword === 'Maddy123!?'
      );
      setIsLoading(false);
    }, 2000); // simulate skeleton for 2 seconds

    return () => clearTimeout(timer);
  }, []);

  return (
    <div>
      <LandingPage isLoading={isLoading} isLogin={isLogin} />
    </div>
  );
}
