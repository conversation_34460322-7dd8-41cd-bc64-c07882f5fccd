{"name": "nxvoy-holiday-planner-fe", "version": "2.1.0", "author": "NxVoy", "license": "", "private": true, "type": "module", "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "eslint": "next lint --fix --dir src", "prettier": "prettier --write .", "lint": "yarn eslint && yarn prettier", "format": "next lint --fix && prettier '**/*.{json,yaml}' --write --ignore-path .gitignore", "prepare": "husky"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,md,ts,tsx,html,css,scss}": "npx prettier --write", "*.{js,jsx,ts,tsx}": "eslint --cache --fix --cache-location ./node_modules/.cache/.eslintcache"}, "dependencies": {"@heroui/react": "^2.8.1", "@next/bundle-analyzer": "^15.3.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.59.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "config": "^3.3.12", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.9", "lucide-react": "^0.487.0", "next": "^15.2.5", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.53.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "tailwind-merge": "^3.3.1", "winston": "^3.14.1"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@tailwindcss/cli": "^4.1.4", "@types/node": "^22.15.3", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "7.12.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "14.1.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.0.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "tailwindcss": "^4.1.4", "ts-node": "^10.9.2", "tslib": "^2.8.1", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3"}}