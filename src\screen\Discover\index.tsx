import ChatSkeleton from '@/components/loaders/ChatSkeleton';
import ChatWithShasa from '../landing/ChatWithShasa';
import PrimaryFilter from '@/components/globalComponents/primaryFilter';
import PrimaryFilterSkeleton from '@/components/loaders/PrimaryFilterSkeleton';
import { useEffect, useState } from 'react';
import Explore from './Explore';
import RecentSearches from './RecentSearches';

interface DiscoverPageProps {
  isLoading?: boolean;
  loadingComponents?: {
    filter?: boolean;
    chat?: boolean;
    recommendations?: boolean;
  };
}
const DiscoverPage = ({
  isLoading = false,
  loadingComponents = {},
}: DiscoverPageProps) => {
  const [componentLoading, setComponentLoading] = useState({
    filter: loadingComponents.filter || isLoading,
    chat: loadingComponents.chat || isLoading,
    recommendations: loadingComponents.recommendations || isLoading,
  });
  // Simulate staggered loading for better UX
  useEffect(() => {
    if (isLoading) {
      // Filter loads first
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, filter: false }));
      }, 800);

      // Chat loads second
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, chat: false }));
      }, 1200);

      // Recommendations load last
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, recommendations: false }));
      }, 1600);
    }
  }, [isLoading]);
  return (
    <div className="p-3 sm:p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-6 gap-3 sm:gap-4 flex-1 min-h-0">
        {/* Chat Section */}
        <div className="lg:col-span-2 order-2 lg:order-1">
          {componentLoading.chat ? <ChatSkeleton /> : <ChatWithShasa />}
          <div className="mt-4">
            {/* <p className='text-xl font-bold mb-3'>map</p> */}
            {/* <Image
              src="/map.png"
              alt="map"
              width={200}
              height={300}
              className="w-full h-[350px] object-cover rounded-xl"
            /> */}
          </div>
        </div>

        {/* Recommendations Section */}
        <div className="lg:col-span-4 order-1 lg:order-2">
          {/* Primary Filter Section */}
          <div className="">
            {componentLoading.filter ? (
              <PrimaryFilterSkeleton />
            ) : (
              <PrimaryFilter />
            )}
          </div>
          <Explore />
          <RecentSearches />
        </div>
      </div>
    </div>
  );
};

export default DiscoverPage;
