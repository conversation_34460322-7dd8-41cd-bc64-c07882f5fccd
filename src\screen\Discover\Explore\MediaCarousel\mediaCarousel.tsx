import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';

import MediaCardComp from './card';

const data = [
  {
    img: 'https://heroui.com/images/hero-card.jpeg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },
  {
    img: 'https://heroui.com/images/album-cover.png',
    heading: 'Exquisite Kerala',
    description: 'Starting at $1099 per person',
  },
  {
    img: 'https://heroui.com/images/hero-card.jpeg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },
  {
    img: 'https://heroui.com/images/album-cover.png',
    heading: 'Exquisite Kerala',
    description: 'Starting at $1099 per person',
  },
  {
    img: 'https://heroui.com/images/hero-card.jpeg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },

  {
    img: 'https://heroui.com/images/album-cover.png',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },
  {
    img: 'https://heroui.com/images/album-cover.png',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },

  {
    img: 'https://heroui.com/images/hero-card.jpeg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },

  {
    img: 'https://heroui.com/images/hero-card.jpeg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },

  {
    img: 'https://heroui.com/images/hero-card.jpeg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },
];

const RecentProduct = () => {
  return (
    <div className="px-10">
      <Carousel className="w-full mb-0">
        <CarouselContent className="overflow-visible">
          {data.map((item, index) => (
            <CarouselItem
              key={`media-item-${item.heading}-${index}`}
              className="md:basis-1/2 lg:basis-1/3 xl:basis-1/4 py-6"
            >
              <MediaCardComp item={item} />
            </CarouselItem>
          ))}
        </CarouselContent>
        {/* Custom Previous Button with Icon */}
        <CarouselPrevious
          className="absolute -left-12 top-1/2 -translate-y-1/2 rounded-full cursor-pointer"
          variant="ghost"
        >
          <FiChevronLeft className="w-6 h-6" />
        </CarouselPrevious>

        {/* Custom Next Button with Icon */}
        <CarouselNext
          className="absolute -right-12 top-1/2 -translate-y-1/2 text-subtitle p-2 rounded-full"
          variant="ghost"
        >
          <FiChevronRight className="w-6 h-6" />
        </CarouselNext>
      </Carousel>
    </div>
  );
};

export default RecentProduct;
