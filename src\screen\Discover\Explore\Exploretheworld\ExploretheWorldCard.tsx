'use client';

import { Card, CardFooter, Image } from '@heroui/react';
import { FaRegStar, FaStar } from 'react-icons/fa';

interface ExploretheWorldCardProps {
  item: {
    image: string;
    name: string;
    rating: number;
  };
}

export default function ExploretheWorldCard({
  item,
}: ExploretheWorldCardProps) {
  return (
    <Card
      isFooterBlurred
      className="border-none rounded-md w-full"
      radius="lg"
    >
      <Image
        alt={item.image}
        className="object-cover rounded-md min-w-full"
        height={290}
        src={item.image}
        width="100%"
      />
      <CardFooter className="flex flex-col items-start before:bg-white/10 border-white/20 border-1 overflow-hidden py-1 absolute before:rounded-xl rounded-large bottom-1 w-[calc(100%_-_8px)] ml-1 z-10">
        <p className="font-bold text-base text-white">{item.name}</p>
        <div className="flex flex-row gap-1">
          {Array(5)
            .fill(0)
            .map((_, i) => (
              <div key={`star-${i}`}>
                {i < item.rating ? (
                  <FaStar fill="#FFC107" />
                ) : (
                  <FaRegStar fill="#FFC107" />
                )}
              </div>
            ))}
        </div>
      </CardFooter>
    </Card>
  );
}
